# Custom 404 Error Page Configuration
ErrorDocument 404 /404.html

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching for better performance
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# URL Rewrite Rules
RewriteEngine On

# Clean URL rewrites for specific pages (must come before trailing slash removal)
RewriteRule ^feedback/?$ feedback.html [NC,L]

# Remove trailing slashes for better SEO (but skip if it's a directory)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^ %1 [R=301,L]

# Redirect common misspellings and old URLs to relevant games
RedirectMatch 301 ^/memory/?$ /memoryGame
RedirectMatch 301 ^/snake/?$ /snake-game
RedirectMatch 301 ^/tetris/?$ /tetris-game
RedirectMatch 301 ^/2048/?$ /2048
RedirectMatch 301 ^/breakout/?$ /breakout-game
RedirectMatch 301 ^/poker/?$ /texas-holdem-game
RedirectMatch 301 ^/sudoku/?$ /sudoku-game

# Force HTTPS (uncomment if you have SSL certificate)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Set proper MIME types for search engines
<IfModule mod_mime.c>
    AddType application/xml .xml
    AddType text/plain .txt
    AddType application/rss+xml .rss
</IfModule>

# Allow search engine bots to access robots.txt and sitemap.xml
<Files "robots.txt">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

<Files "sitemap.xml">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

# Prevent access to sensitive files
<Files ".htaccess">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

<Files "*.log">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>
